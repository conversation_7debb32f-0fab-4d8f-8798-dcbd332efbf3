#ifndef VECTOR_DEBUG_H
#define VECTOR_DEBUG_H

#include <stdexcept>
#include <string>
#include <vector>
#include <array>
#include <iostream>
#include <spdlog/spdlog.h>
#include <type_traits>

// Enable debug mode for vector access checking
#ifdef _DEBUG
#define DEBUG_VECTOR_ACCESS 1
#else
#define DEBUG_VECTOR_ACCESS 0
#endif

#if DEBUG_VECTOR_ACCESS

// Enhanced safe vector access with stack trace
#define SAFE_VECTOR_ACCESS(vec, i, context) \
    ([&]() -> auto& { \
        if ((i) >= (vec).size()) { \
            std::string error_msg = "Vector out of range in " + std::string(context) + \
                                  ": index " + std::to_string(i) + \
                                  " >= size " + std::to_string((vec).size()) + \
                                  " at " + std::string(__FILE__) + ":" + std::to_string(__LINE__); \
            spdlog::error("VECTOR_DEBUG: {}", error_msg); \
            std::cerr << "VECTOR_DEBUG: " << error_msg << std::endl; \
            std::cerr << "Stack trace: " << __FUNCTION__ << " in " << __FILE__ << ":" << __LINE__ << std::endl; \
            throw std::out_of_range(error_msg); \
        } \
        spdlog::trace("VECTOR_ACCESS: {} - accessing [{}] of size {} at {}:{}", \
                     context, i, (vec).size(), __FILE__, __LINE__); \
        return (vec)[i]; \
    }())

// Enhanced safe array access
#define SAFE_ARRAY_ACCESS(arr, i, max_size, context) \
    ([&]() -> auto& { \
        if ((i) >= (max_size)) { \
            std::string error_msg = "Array out of range in " + std::string(context) + \
                                  ": index " + std::to_string(i) + \
                                  " >= max_size " + std::to_string(max_size) + \
                                  " at " + std::string(__FILE__) + ":" + std::to_string(__LINE__); \
            spdlog::error("VECTOR_DEBUG: {}", error_msg); \
            std::cerr << "VECTOR_DEBUG: " << error_msg << std::endl; \
            std::cerr << "Stack trace: " << __FUNCTION__ << " in " << __FILE__ << ":" << __LINE__ << std::endl; \
            throw std::out_of_range(error_msg); \
        } \
        spdlog::trace("ARRAY_ACCESS: {} - accessing [{}] of max_size {} at {}:{}", \
                     context, i, max_size, __FILE__, __LINE__); \
        return (arr)[i]; \
    }())

// Safe std::array access
#define SAFE_STD_ARRAY_ACCESS(arr, i, context) \
    ([&]() -> auto& { \
        constexpr size_t array_size = std::tuple_size_v<std::decay_t<decltype(arr)>>; \
        if ((i) >= array_size) { \
            std::string error_msg = "std::array out of range in " + std::string(context) + \
                                  ": index " + std::to_string(i) + \
                                  " >= size " + std::to_string(array_size) + \
                                  " at " + std::string(__FILE__) + ":" + std::to_string(__LINE__); \
            spdlog::error("VECTOR_DEBUG: {}", error_msg); \
            std::cerr << "VECTOR_DEBUG: " << error_msg << std::endl; \
            std::cerr << "Stack trace: " << __FUNCTION__ << " in " << __FILE__ << ":" << __LINE__ << std::endl; \
            throw std::out_of_range(error_msg); \
        } \
        spdlog::trace("STD_ARRAY_ACCESS: {} - accessing [{}] of size {} at {}:{}", \
                     context, i, array_size, __FILE__, __LINE__); \
        return (arr)[i]; \
    }())

// Log vector access patterns with more detail
#define LOG_VECTOR_ACCESS(vec, i, context) \
    do { \
        spdlog::trace("VECTOR_ACCESS: {} - accessing [{}] of size {} at {}:{} in {}", \
                     context, i, (vec).size(), __FILE__, __LINE__, __FUNCTION__); \
        std::cerr << "VECTOR_ACCESS: " << context << " - accessing [" << i \
                  << "] of size " << (vec).size() << " at " << __FILE__ << ":" << __LINE__ << std::endl; \
    } while(0)

// Validate vector before any operations
#define VALIDATE_VECTOR(vec, context) \
    do { \
        if ((vec).empty()) { \
            spdlog::warn("VECTOR_DEBUG: Empty vector in {} at {}:{}", \
                        context, __FILE__, __LINE__); \
        } \
        if ((vec).size() > 100000000) { /* 100MB threshold */ \
            spdlog::warn("VECTOR_DEBUG: Suspiciously large vector size {} ({:.2f}MB) in {} at {}:{}", \
                        (vec).size(), (vec).size() / (1024.0 * 1024.0), context, __FILE__, __LINE__); \
        } \
    } while(0)

// Validate large memory buffers (for known large allocations like physical memory)
#define VALIDATE_LARGE_MEMORY_BUFFER(vec, context, expected_max_gb) \
    do { \
        if ((vec).empty()) { \
            spdlog::warn("VECTOR_DEBUG: Empty large memory buffer in {} at {}:{}", \
                        context, __FILE__, __LINE__); \
        } \
        constexpr size_t max_size = static_cast<size_t>(expected_max_gb) * 1024ULL * 1024ULL * 1024ULL; \
        if ((vec).size() > max_size) { \
            spdlog::error("VECTOR_DEBUG: Large memory buffer size {} ({:.2f}GB) exceeds expected maximum {:.2f}GB in {} at {}:{}", \
                         (vec).size(), (vec).size() / (1024.0 * 1024.0 * 1024.0), \
                         expected_max_gb, context, __FILE__, __LINE__); \
        } else if ((vec).size() > 1024ULL * 1024ULL * 1024ULL) { \
            spdlog::debug("VECTOR_DEBUG: Large memory buffer validated: size {} ({:.2f}GB) in {} at {}:{}", \
                         (vec).size(), (vec).size() / (1024.0 * 1024.0 * 1024.0), \
                         context, __FILE__, __LINE__); \
        } else { \
            spdlog::trace("VECTOR_DEBUG: Memory buffer validated: size {} ({:.2f}MB) in {} at {}:{}", \
                         (vec).size(), (vec).size() / (1024.0 * 1024.0), \
                         context, __FILE__, __LINE__); \
        } \
    } while(0)

#else

// Release mode - no overhead
#define SAFE_VECTOR_ACCESS(vec, i, context) (vec)[i]
#define SAFE_ARRAY_ACCESS(arr, i, max_size, context) (arr)[i]
#define SAFE_STD_ARRAY_ACCESS(arr, i, context) (arr)[i]
#define LOG_VECTOR_ACCESS(vec, i, context) do {} while(0)
#define VALIDATE_VECTOR(vec, context) do {} while(0)
#define VALIDATE_LARGE_MEMORY_BUFFER(vec, context, expected_max_gb) do {} while(0)

#endif

// Always-on bounds checking for critical sections
#define CRITICAL_VECTOR_ACCESS(vec, i, context) \
    ([&]() -> auto& { \
        if ((i) >= (vec).size()) { \
            std::string error_msg = "CRITICAL: Vector out of range in " + std::string(context) + \
                                  ": index " + std::to_string(i) + \
                                  " >= size " + std::to_string((vec).size()) + \
                                  " at " + std::string(__FILE__) + ":" + std::to_string(__LINE__); \
            spdlog::critical("VECTOR_DEBUG: {}", error_msg); \
            std::cerr << "CRITICAL VECTOR_DEBUG: " << error_msg << std::endl; \
            std::abort(); /* Immediate termination for critical errors */ \
        } \
        return (vec)[i]; \
    }())

// Helper function to validate vector access before it happens
template<typename T>
inline bool ValidateVectorAccess(const std::vector<T>& vec, size_t index, const std::string& context) {
    if (index >= vec.size()) {
        spdlog::error("VECTOR_DEBUG: Invalid vector access in {}: index {} >= size {}", 
                     context, index, vec.size());
        return false;
    }
    return true;
}

// Helper function to validate array access before it happens
template<typename T, size_t N>
inline bool ValidateArrayAccess(const std::array<T, N>& arr, size_t index, const std::string& context) {
    if (index >= N) {
        spdlog::error("VECTOR_DEBUG: Invalid array access in {}: index {} >= size {}", 
                     context, index, N);
        return false;
    }
    return true;
}

// Safe vector iterator access
template<typename Iterator>
inline bool ValidateIteratorAccess(Iterator it, Iterator begin, Iterator end, const std::string& context) {
    if (it < begin || it >= end) {
        spdlog::error("VECTOR_DEBUG: Invalid iterator access in {}: iterator out of range", context);
        return false;
    }
    return true;
}

// Safe map access
template<typename Map, typename Key>
inline auto SafeMapAccess(Map& map, const Key& key, const std::string& context) -> decltype(map.find(key)) {
    auto it = map.find(key);
    if (it == map.end()) {
        spdlog::warn("VECTOR_DEBUG: Key not found in map access for {}", context);
    }
    return it;
}

#endif // VECTOR_DEBUG_H